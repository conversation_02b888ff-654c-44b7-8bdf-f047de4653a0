# 前端开发规范

## 1. 组件命名规范

### 1.1 自定义 Vue 组件命名
- **文件名**: 使用小写字母，不使用连字符
- **组件名**: 使用 PascalCase（大驼峰命名）

```javascript
// ✅ 正确示例
// 文件名: userprofile.vue
export default {
  name: 'UserProfile'
}

// 文件名: productcard.vue
export default {
  name: 'ProductCard'
}

// ❌ 错误示例
// 文件名: UserProfile.vue 或 user-profile.vue
export default {
  name: 'userProfile' // 应该是 PascalCase
}
```

### 1.2 Vuetify 组件命名
- **必须使用完整的大写组件名**，不使用简写形式
- 保持 Vuetify 官方的 PascalCase 命名约定

```vue
<!-- ✅ 正确示例 -->
<template>
  <VContainer>
    <VRow>
      <VCol>
        <VBtn color="primary" size="large">
          <VIcon icon="mdi-plus" />
          添加
        </VBtn>
        <VCard>
          <VCardTitle>标题</VCardTitle>
          <VCardText>内容</VCardText>
          <VCardActions>
            <VSpacer />
            <VBtn>取消</VBtn>
            <VBtn color="primary">确认</VBtn>
          </VCardActions>
        </VCard>
      </VCol>
    </VRow>
  </VContainer>
</template>

<!-- ❌ 错误示例 -->
<template>
  <v-container>  <!-- 应该使用 VContainer -->
    <v-btn>按钮</v-btn>  <!-- 应该使用 VBtn -->
    <v-card>  <!-- 应该使用 VCard -->
      <v-card-title>标题</v-card-title>  <!-- 应该使用 VCardTitle -->
    </v-card>
  </v-container>
</template>
```

## 2. 文件和目录结构规范

### 2.1 目录命名
- 使用小写字母，不使用连字符
- 保持语义化和层次清晰

```
src/
├── components/          # 通用组件
│   ├── common/         # 公共组件
│   ├── forms/          # 表单组件
│   └── ui/             # UI 组件
├── pages/              # 页面组件
│   ├── user/           # 用户相关页面
│   ├── product/        # 产品相关页面
│   └── base/           # 基础页面
├── layouts/            # 布局组件
├── stores/             # Pinia 状态管理
├── utils/              # 工具函数
├── services/           # API 服务
└── assets/             # 静态资源
```

### 2.2 文件命名
- Vue 组件文件：小写，以 `.vue` 结尾
- JavaScript 文件：小写，以 `.js` 结尾
- 样式文件：小写，以 `.css/.scss` 结尾

```
✅ 正确示例:
userprofile.vue
productlist.vue
apiservice.js
commonutils.js

❌ 错误示例:
UserProfile.vue
user-profile.vue
ApiService.js
api-service.js
```

## 3. Vue 组件编写规范

### 3.1 组件结构顺序
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
// 导入语句
import { mapState } from 'pinia'

export default {
  name: 'ComponentName',
  components: {
    // 组件注册
  },
  props: {
    // 属性定义
  },
  data() {
    return {
      // 数据定义
    }
  },
  computed: {
    // 计算属性
  },
  methods: {
    // 方法定义
  }
}
</script>

<style scoped>
/* 样式定义 */
</style>
```

### 3.2 Props 定义规范
```javascript
// ✅ 正确示例
props: {
  title: {
    type: String,
    required: true
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  isVisible: {
    type: Boolean,
    default: false
  }
}

// ❌ 错误示例
props: ['title', 'size', 'isVisible']  // 缺少类型定义
```

## 4. Pinia 状态管理规范

### 4.1 Store 文件命名
- 使用小写 + `store.js` 后缀

```
✅ 正确示例:
userstore.js
productstore.js
appstore.js

❌ 错误示例:
userStore.js
UserStore.js
user-store.js
```

### 4.2 Store 定义规范
```javascript
// ✅ 正确示例
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    currentUser: null,
    isLoggedIn: false,
    userList: []
  }),
  
  getters: {
    fullName: (state) => {
      return state.currentUser ? 
        `${state.currentUser.firstName} ${state.currentUser.lastName}` : ''
    }
  },
  
  actions: {
    async fetchUser(userId) {
      try {
        // API 调用逻辑
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },
    
    setUser(user) {
      this.currentUser = user
      this.isLoggedIn = true
    }
  }
})
```

## 5. 代码格式化规范

### 5.1 缩进和空格
- 使用 2 个空格缩进
- 操作符前后加空格
- 逗号后加空格

### 5.2 引号使用
- JavaScript 中优先使用单引号
- HTML 属性使用双引号

```javascript
// ✅ 正确示例
const message = 'Hello World'
const config = {
  apiUrl: 'https://api.example.com',
  timeout: 5000
}

// HTML 中
<VBtn color="primary" size="large">按钮</VBtn>
```

## 6. 注释规范

### 6.1 组件注释
```javascript
/**
 * 用户资料组件
 * @description 显示和编辑用户基本信息
 * <AUTHOR>
 * @date 2024-01-01
 */
export default {
  name: 'UserProfile',
  // ...
}
```

### 6.2 方法注释
```javascript
/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页数量
 * @returns {Promise<Array>} 用户列表
 */
async fetchUserList(params) {
  // 实现逻辑
}
```

## 7. 错误处理规范

### 7.1 API 调用错误处理
```javascript
// ✅ 正确示例
async fetchData() {
  try {
    const response = await apiService.getData()
    this.data = response.data
  } catch (error) {
    console.error('获取数据失败:', error)
    // 用户友好的错误提示
    this.$toast.error('数据加载失败，请稍后重试')
  }
}
```

## 8. 性能优化规范

### 8.1 组件懒加载
```javascript
// 路由懒加载
const UserProfile = () => import('@/pages/user/userprofile.vue')

// 组件懒加载
components: {
  UserProfile: () => import('@/components/user/userprofile.vue')
}
```

### 8.2 计算属性使用
```javascript
// ✅ 使用计算属性缓存复杂计算
computed: {
  filteredList() {
    return this.list.filter(item => item.status === 'active')
  }
}

// ❌ 避免在模板中进行复杂计算
// <div v-for="item in list.filter(item => item.status === 'active')" :key="item.id">
```

这个规范文档涵盖了您提到的核心要求：
1. 前端页面组件使用小写命名
2. Vuetify 组件使用完整的大写组件名（如 VBtn 而不是 v-btn）
3. 建立了完整的编码规范体系

您希望我继续完善这个规范文档的其他部分吗？
