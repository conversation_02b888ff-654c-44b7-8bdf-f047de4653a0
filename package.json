{"name": "frontend", "version": "0.0.1", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "lint": "eslint . --fix --ignore-path .gitignore", "format": "prettier .  --write"}, "dependencies": {"@mdi/font": "7.0.96", "axios": "^1.2.1", "pinia": "^2.0.32", "roboto-fontface": "*", "vue": "^3.2.47", "vue-router": "^4.1.6", "vuetify": "^3.1.3", "webfontloader": "^1.0.0"}, "devDependencies": {"js-cookie": "^3.0.1", "@faker-js/faker": "^7.6.0", "miragejs": "^0.1.47", "@vitejs/plugin-vue": "^4.0.0", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "vite": "^4.1.4", "vite-plugin-vuetify": "^1.0.0-alpha.12", "@rushstack/eslint-patch": "^1.2.0", "@vue/eslint-config-prettier": "^7.1.0", "@vue/test-utils": "^2.3.0", "eslint-config-prettier": "^8.5.0", "jsdom": "^21.1.0", "prettier": "2.8.4", "vitest": "^0.29.1"}}