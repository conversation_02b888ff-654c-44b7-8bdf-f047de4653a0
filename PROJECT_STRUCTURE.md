# 项目结构说明

## 📁 重构后的目录结构

```
src/
├── components/                 # 通用可复用组件
│   ├── layout/                # 布局组件
│   │   ├── sidebar.vue        # 侧边栏
│   │   ├── topheader.vue      # 顶部导航
│   │   ├── maincontent.vue    # 主内容区域
│   │   ├── chatinput.vue      # 聊天输入框
│   │   └── usermenu.vue       # 用户菜单
│   └── common/                # 通用UI组件
│       ├── buttons/           # 按钮组件
│       ├── forms/             # 表单组件
│       ├── ui/                # 基础UI组件
│       ├── modals/            # 模态框组件
│       └── cards/             # 卡片组件
├── pages/                     # 页面级组件
│   ├── auth/                  # 认证相关页面
│   │   └── login.vue
│   ├── base/                  # 基础页面
│   │   ├── home.vue
│   │   ├── page404.vue
│   │   └── test.vue
│   ├── chat/                  # 聊天功能模块
│   │   ├── index.vue          # 聊天主页面
│   │   └── components/        # 聊天页面专用组件
│   │       └── chatmessage.vue
│   ├── welcome/               # 欢迎页面模块
│   │   ├── index.vue          # 欢迎主页面
│   │   └── components/        # 欢迎页面专用组件
│   │       ├── welcomesection.vue
│   │       ├── featurecards.vue
│   │       └── featurecard.vue
│   ├── settings/              # 设置功能模块
│   │   ├── index.vue          # 设置主页面
│   │   └── components/        # 设置页面专用组件
│   │       └── usersettings.vue
│   ├── contact/               # 联系我们模块
│   │   ├── index.vue          # 联系主页面
│   │   └── components/        # 联系页面专用组件
│   │       └── contactus.vue
│   └── user/                  # 用户相关页面
├── layouts/                   # 布局模板
├── router/                    # 路由配置
├── stores/                    # 状态管理
├── plugins/                   # 插件配置
└── assets/                    # 静态资源
```

## 🎯 重构原则

### 1. 页面级组件 (pages/)
- **职责**: 负责整个页面的逻辑和布局
- **特点**: 
  - 每个业务功能一个文件夹
  - 包含主页面文件 `index.vue`
  - 包含该页面专用的组件在 `components/` 子目录
- **示例**: `pages/chat/index.vue` 是聊天页面的主入口

### 2. 通用组件 (components/)
- **职责**: 可在多个页面复用的UI组件
- **特点**:
  - 无业务逻辑，专注UI展示
  - 通过props接收数据
  - 可配置、可复用
- **分类**:
  - `layout/`: 布局相关组件
  - `common/`: 通用UI组件

### 3. 页面专用组件 (pages/*/components/)
- **职责**: 只在特定页面使用的组件
- **特点**:
  - 包含特定业务逻辑
  - 与父页面紧密耦合
  - 不需要在其他地方复用

## 🔄 重构内容

### 移动的组件:
1. `components/chat/chatmessage.vue` → `pages/chat/components/chatmessage.vue`
2. `components/welcome/*` → `pages/welcome/components/*`
3. `components/settings/usersettings.vue` → `pages/settings/components/usersettings.vue`
4. `components/contact/contactus.vue` → `pages/contact/components/contactus.vue`

### 新增的页面:
1. `pages/chat/index.vue` - 聊天页面主入口
2. `pages/welcome/index.vue` - 欢迎页面主入口
3. `pages/settings/index.vue` - 设置页面主入口
4. `pages/contact/index.vue` - 联系页面主入口

### 更新的文件:
1. `components/layout/maincontent.vue` - 使用新的页面组件
2. `router/base.routes.js` - 更新路由配置

## 🎨 架构优势

1. **清晰的职责分离**: 页面组件和通用组件职责明确
2. **模块化组织**: 每个功能模块独立管理
3. **易于维护**: 相关文件集中在同一目录
4. **可扩展性**: 新增功能时结构清晰
5. **复用性**: 通用组件可在多处使用

## 📝 开发规范

1. **页面组件命名**: 使用 `index.vue` 作为页面主入口
2. **组件导入**: 优先使用相对路径导入同模块组件
3. **文件组织**: 相关功能的文件放在同一目录下
4. **组件复用**: 确认组件是否真的需要复用再放入 `components/common/`
