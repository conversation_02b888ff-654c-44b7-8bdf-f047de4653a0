<script setup>
import { computed } from "vue"
import { useChatStore } from "@/stores/baseStore"
import WelcomePage from "@/pages/welcome/index.vue"
import ChatPage from "@/pages/chat/index.vue"

// 使用聊天store
const chatStore = useChatStore()

// 计算属性
const showWelcome = computed(() => {
  // 显示欢迎页面的条件：
  // 1. showWelcome为true，或者
  // 2. 当前对话没有消息
  return chatStore.showWelcome || chatStore.currentMessages.length === 0
})
</script>

<template>
  <div class="main-content">
    <!-- 欢迎页面 -->
    <WelcomePage v-if="showWelcome" />

    <!-- 聊天页面 -->
    <ChatPage v-else />
  </div>
</template>

<style scoped>
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background-color: rgb(var(--v-theme-surface));
  padding-bottom: 120px;
}

/* 美化主内容区域滚动条 - 与侧边栏保持一致 */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.main-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.main-content:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.main-content::-webkit-scrollbar-thumb:active {
  background: rgba(0, 0, 0, 0.4);
}

/* Firefox滚动条样式 */
.main-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding-bottom: 80px; /* 为底部导航留出空间 */
  }

  /* 移动端滚动条更细 */
  .main-content::-webkit-scrollbar {
    width: 4px;
  }
}
</style>
