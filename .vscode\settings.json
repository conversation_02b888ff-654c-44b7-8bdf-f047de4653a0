{"editor.formatOnSave": false, "editor.renderWhitespace": "all", "editor.bracketPairColorization.enabled": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit"}, "eslint.format.enable": true, "[javascript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.formatOnSave": true, "editor.defaultFormatter": "vscode.json-language-features"}}