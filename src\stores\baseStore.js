import { defineStore } from "pinia"

export const useAppStore = defineStore("app", {
  state: () => ({
    // 应用全局状态
    loading: false,
    theme: 'light',
  }),
  actions: {
    setLoading(loading) {
      this.loading = loading
    },
    toggleTheme() {
      this.theme = this.theme === 'light' ? 'dark' : 'light'
    },
  },
})

// 聊天状态管理
export const useChatStore = defineStore("chat", {
  state: () => ({
    // 聊天相关状态
    messages: [],
    currentConversationId: null,
    isLoading: false,
    showWelcome: true,
  }),

  getters: {
    // 获取当前对话的消息
    currentMessages: (state) => {
      return state.messages.filter(msg =>
        msg.conversationId === state.currentConversationId
      )
    },

    // 检查是否有消息
    hasMessages: (state) => {
      return state.messages.length > 0
    }
  },

  actions: {
    // 添加用户消息
    addUserMessage(content) {
      const message = {
        id: Date.now(),
        type: 'user',
        content: content.trim(),
        timestamp: new Date(),
        conversationId: this.currentConversationId || this.createNewConversation()
      }

      this.messages.push(message)
      this.showWelcome = false

      return message
    },

    // 添加AI回复
    addAIMessage(content) {
      const message = {
        id: Date.now() + 1,
        type: 'ai',
        content: content,
        timestamp: new Date(),
        conversationId: this.currentConversationId
      }

      this.messages.push(message)
      return message
    },

    // 创建新对话
    createNewConversation() {
      const conversationId = 'conv_' + Date.now()
      this.currentConversationId = conversationId
      this.showWelcome = true
      this.isLoading = false
      return conversationId
    },

    // 设置加载状态
    setLoading(loading) {
      this.isLoading = loading
    },

    // 切换欢迎页面显示
    setShowWelcome(show) {
      this.showWelcome = show
    },

    // 清空当前对话
    clearCurrentConversation() {
      if (this.currentConversationId) {
        this.messages = this.messages.filter(msg =>
          msg.conversationId !== this.currentConversationId
        )
      }
      this.showWelcome = true
    }
  }
})
