<script setup>
import { computed } from 'vue'
import { useChatStore } from '@/stores/baseStore'
import ChatMessage from './components/chatmessage.vue'

// 使用聊天store
const chatStore = useChatStore()

// 计算属性
const chatMessages = computed(() => chatStore.currentMessages)

// 方法
const handleBackToWelcome = () => {
  chatStore.createNewConversation()
  console.log('返回欢迎页面')
}
</script>

<template>
  <div class="chat-page">
    <!-- 聊天消息区域 -->
    <div class="chat-messages">
      <VContainer>
        <div
          v-if="chatMessages.length === 0"
          class="empty-chat"
        >
          <VBtn
            variant="outlined"
            prepend-icon="mdi-arrow-left"
            @click="handleBackToWelcome"
          >
            返回首页
          </VBtn>
        </div>

        <!-- 显示聊天消息 -->
        <div
          v-for="message in chatMessages"
          :key="message.id"
          class="message-item"
        >
          <ChatMessage :message="message" />
        </div>
      </VContainer>
    </div>
  </div>
</template>

<style scoped>
.chat-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.empty-chat {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  text-align: center;
}

.message-item {
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-messages {
    padding: 8px 0;
  }
}
</style>
